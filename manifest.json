{"manifest_version": 3, "name": "OneTab Plus", "version": "1.7.1", "description": "A better OneTab extension for Chrome", "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https://*.supabase.co https://*.gravatar.com https://*.githubusercontent.com https://bolt.new https://lovable.dev https://devv.ai https://res.wx.qq.com https://assets.cnblogs.com https://www.spring-doc.cn https://chat.deepseek.com https://static.figma.com; font-src 'self' data:; connect-src 'self' https://*.supabase.co; frame-src 'none'; base-uri 'self'; form-action 'self';"}, "permissions": ["tabs", "storage", "unlimitedStorage", "notifications", "identity", "contextMenus"], "host_permissions": ["https://*.supabase.co/*"], "action": {"default_icon": "icons/icon16.png", "default_title": "OneTab Plus - 标签页管理器"}, "web_accessible_resources": [{"resources": ["icons/*.png"], "matches": ["chrome-extension://*/*"]}, {"resources": ["src/auth/*.html"], "matches": ["https://*.supabase.co/*"]}], "background": {"service_worker": "src/service-worker.ts", "type": "module"}, "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "commands": {"_execute_action": {"suggested_key": {"default": "Ctrl+Shift+S", "mac": "Command+Shift+S"}, "description": "Open tab manager"}, "save_all_tabs": {"suggested_key": {"default": "Alt+Shift+S", "mac": "Alt+Shift+S"}, "description": "Save all tabs"}, "save_current_tab": {"suggested_key": {"default": "Alt+S", "mac": "Alt+S"}, "description": "Save current tab"}}}